# AI Commit Message Generator

A VS Code extension that generates intelligent commit messages using Google Gemini AI based on your staged Git changes.

## Features

- 🤖 **AI-Powered**: Uses Google Gemini AI to analyze your code changes and generate meaningful commit messages
- 🔒 **Secure**: API keys are stored securely using VS Code's SecretStorage API
- ⚡ **Fast**: Quick generation with configurable models
- 🎯 **Smart**: Follows conventional commit format when applicable
- 🛡️ **Robust**: Handles API rate limits, token limits, and error cases gracefully

## Installation

1. Clone this repository
2. Open the project in VS Code
3. Run `npm install` to install dependencies
4. Press `F5` to launch the extension in a new Extension Development Host window

## Setup

**🎉 Ready to Use Out of the Box!**

The extension comes with a default API key and works immediately after installation. However, for better performance and quota management, you can set your own API key:

1. **Get a Gemini API Key** (Optional):
   - Go to [Google AI Studio](https://aistudio.google.com/)
   - Create a new API key
   - Copy the key (starts with `AIzaSy...`)

2. **Set your API Key** (Optional):
   - Open Command Palette (`Ctrl+Shift+P` / `Cmd+Shift+P`)
   - Run `Set Gemini API Key`
   - Paste your API key when prompted
   - The key will be stored securely in your system's credential manager

## Usage

**No setup required!** Just install and start using:

1. **Stage your changes**: Use `git add` or VS Code's Source Control panel to stage the files you want to commit

2. **Generate commit message**:
   - Click the sparkle (✨) icon in the Source Control panel, OR
   - Open Command Palette and run `Generate AI Commit Message`

3. **Review and commit**: The generated message will appear in the commit message box. Review it and commit as usual.

## Configuration

You can customize the extension through VS Code settings:

- **Token Limit**: Set maximum tokens to send to API (default: 100,000)

- **Custom Prompt**: Add a custom prefix to the prompt (optional)

**Note**: The extension now uses a fixed, optimized Gemini model endpoint for consistent performance.

## Security Features

- ✅ API keys stored in OS credential manager (Keychain/Credential Manager/libsecret)
- ✅ No plaintext storage of sensitive data
- ✅ Extension-sandboxed secret storage
- ✅ No accidental commits of API keys

## Error Handling

- **Rate Limiting**: Graceful handling of API rate limits with user-friendly messages
- **Token Limits**: Warns when diffs are too large and may exceed API limits
- **Network Issues**: Clear error messages for connection problems
- **Invalid API Keys**: Prompts to set a new key when authentication fails

## Development

### Building

```bash
npm install
npm run compile
```

### Testing

1. Open the project in VS Code
2. Press `F5` to launch Extension Development Host
3. Open a Git repository in the new window
4. Stage some changes and test the extension

### Packaging

```bash
npm install -g vsce
vsce package
```

## Requirements

- VS Code 1.85.0 or higher
- Git installed and available in PATH
- Google Gemini API key

## License

MIT License - see LICENSE file for details

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Guidlines for PR

1. Please create a new branch for your changes
2. Please make sure to update tests as appropriate
3. Please make sure to run `npm run compile` before committing
4. Please make sure to run `vsce package` before submitting a PR
5. Please make sure to test your changes before submitting a PR
6. Please make sure to update the version number in `package.json` before submitting a PR