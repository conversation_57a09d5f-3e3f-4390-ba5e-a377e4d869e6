{"name": "ai-commit-generator", "displayName": "AI Commit Message Generator", "description": "Generate intelligent commit messages using Google Gemini AI", "version": "1.0.0", "engines": {"vscode": "^1.85.0"}, "categories": ["Other"], "activationEvents": ["onCommand:ai-commit-generator.generate", "onCommand:ai-commit-generator.setApiKey"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "ai-commit-generator.generate", "title": "Generate AI Commit Message", "icon": "$(sparkle)"}, {"command": "ai-commit-generator.setApiKey", "title": "Set Gemini API Key"}], "menus": {"scm/title": [{"command": "ai-commit-generator.generate", "when": "scmProvider == 'git'", "group": "navigation"}]}, "configuration": {"title": "AI Commit Generator", "properties": {"ai-commit-generator.model": {"type": "string", "default": "gemini-2.5-flash-lite-preview-06-17", "enum": ["gemini-2.5-flash-lite-preview-06-17", "gemini-2.0-flash", "gemini-1.5-pro"], "description": "Gemini model to use for generating commit messages"}, "ai-commit-generator.maxTokens": {"type": "number", "default": 100000, "description": "Maximum number of tokens to send to the API"}, "ai-commit-generator.customPrompt": {"type": "string", "default": "", "description": "Custom prompt prefix to add before the diff (optional)"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.85.0", "@types/node": "18.x", "typescript": "^5.0.0"}, "dependencies": {"simple-git": "^3.20.0"}}