"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = require("vscode");
const simpleGit = require("simple-git");
const API_KEY_SECRET = 'gemini-api-key';
const DEFAULT_API_ENDPOINT = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent';
const FALLBACK_API_KEY = 'AIzaSyCMiod0mCRxOR2eIcl4fvXKppQpoXYNQ64';
function activate(context) {
    console.log('🚀 AI Commit Generator: Starting activation...');
    // Simple test command first
    const testCommand = vscode.commands.registerCommand('ai-commit-generator.test', () => {
        vscode.window.showInformationMessage('✅ AI Commit Generator is working!');
        console.log('✅ Test command executed successfully');
    });
    // Main generate command
    const generateCommand = vscode.commands.registerCommand('ai-commit-generator.generate', async () => {
        console.log('🎯 Generate command triggered');
        try {
            // Simple test first - just show a message
            vscode.window.showInformationMessage('🤖 Generating commit message...');
            // Get the Git extension
            const gitExtension = vscode.extensions.getExtension('vscode.git');
            if (!gitExtension) {
                vscode.window.showErrorMessage('❌ Git extension not found');
                return;
            }
            if (!gitExtension.isActive) {
                await gitExtension.activate();
            }
            const gitAPI = gitExtension.exports.getAPI(1);
            const repo = gitAPI.repositories[0];
            if (!repo) {
                vscode.window.showErrorMessage('❌ No Git repository found');
                return;
            }
            // Get staged changes
            const git = simpleGit.simpleGit(repo.rootUri.fsPath);
            const diff = await git.diff(['--staged']);
            if (!diff || diff.trim().length === 0) {
                vscode.window.showWarningMessage('⚠️ No staged changes found. Please stage your changes first.');
                return;
            }
            // Call API
            const commitMessage = await callGeminiAPI(FALLBACK_API_KEY, diff, '');
            // Set the commit message
            repo.inputBox.value = commitMessage;
            vscode.window.showInformationMessage('✅ Commit message generated!');
        }
        catch (error) {
            console.error('❌ Error:', error);
            vscode.window.showErrorMessage(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    });
    // API key command
    const setApiKeyCommand = vscode.commands.registerCommand('ai-commit-generator.setApiKey', async () => {
        console.log('🔑 Set API key command triggered');
        await setApiKey(context);
    });
    // Register all commands
    context.subscriptions.push(testCommand, generateCommand, setApiKeyCommand);
    console.log('✅ AI Commit Generator: Extension activated successfully');
    vscode.window.showInformationMessage('🚀 AI Commit Generator is ready!');
}
async function setApiKey(context) {
    const apiKey = await vscode.window.showInputBox({
        prompt: 'Enter your Gemini API key',
        password: true,
        placeHolder: 'AIzaSy...',
        validateInput: (value) => {
            if (!value || value.trim().length === 0) {
                return 'API key cannot be empty';
            }
            if (!value.startsWith('AIzaSy')) {
                return 'Invalid API key format. Gemini API keys start with "AIzaSy"';
            }
            return null;
        }
    });
    if (apiKey) {
        await context.secrets.store(API_KEY_SECRET, apiKey.trim());
        vscode.window.showInformationMessage('API key saved securely!');
    }
}
async function callGeminiAPI(apiKey, diff, customPrompt) {
    const basePrompt = customPrompt ||
        'Generate a concise, clear commit message for the following git diff. ' +
            'Follow conventional commit format if applicable. ' +
            'Focus on what changed and why, not how. ' +
            'Keep it under 72 characters for the first line.';
    const prompt = `${basePrompt}\n\nGit diff:\n${diff}`;
    const requestBody = {
        contents: [{
                parts: [{
                        text: prompt
                    }]
            }],
        generationConfig: {
            temperature: 0.3,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 200,
        }
    };
    const url = `${DEFAULT_API_ENDPOINT}?key=${apiKey}`;
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });
        if (!response.ok) {
            if (response.status === 429) {
                throw new Error('API rate limit exceeded. Please wait a moment and try again.');
            }
            if (response.status === 401) {
                throw new Error('Invalid API key. Please check your Gemini API key.');
            }
            if (response.status === 400) {
                throw new Error('Request too large or malformed. Try staging fewer changes.');
            }
            throw new Error(`API request failed with status ${response.status}`);
        }
        const data = await response.json();
        if (data.error) {
            throw new Error(`API Error: ${data.error.message}`);
        }
        if (!data.candidates || data.candidates.length === 0) {
            throw new Error('No response generated by the API');
        }
        const generatedText = data.candidates[0].content.parts[0].text;
        return generatedText.trim();
    }
    catch (error) {
        if (error instanceof Error) {
            throw error;
        }
        throw new Error('Network error or unexpected response format');
    }
}
function deactivate() {
    // Cleanup if needed
}
//# sourceMappingURL=extension-backup.js.map