{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;AAgCA,4BA0BC;AA2OD,gCAEC;AAvSD,iCAAiC;AACjC,wCAAwC;AAExC,MAAM,cAAc,GAAG,oDAAoD,CAAC;AAC5E,MAAM,oBAAoB,GAAG,6GAA6G,CAAC;AAC3I,MAAM,gBAAgB,GAAG,yCAAyC,CAAC;AA2BnE,SAAgB,QAAQ,CAAC,OAAgC;IACvD,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;IAEpE,oBAAoB;IACpB,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CACrD,8BAA8B,EAC9B,GAAG,EAAE;QACH,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC/D,OAAO,qBAAqB,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC,CACF,CAAC;IAEF,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CACtD,+BAA+B,EAC/B,GAAG,EAAE;QACH,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;QAClE,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC,CACF,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IAErE,wCAAwC;IACxC,uBAAuB,CAAC,OAAO,CAAC,CAAC;IACjC,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;AACvE,CAAC;AAED,KAAK,UAAU,uBAAuB,CAAC,OAAgC;IACrE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IACzD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACvD,6IAA6I,EAC7I,oBAAoB,EACpB,iBAAiB,CAClB,CAAC;QACF,IAAI,MAAM,KAAK,oBAAoB,EAAE,CAAC;YACpC,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;AACH,CAAC;AAED,KAAK,UAAU,SAAS,CAAC,OAAgC;IACvD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;QAC9C,MAAM,EAAE,2BAA2B;QACnC,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,WAAW;QACxB,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;YACvB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxC,OAAO,yBAAyB,CAAC;YACnC,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChC,OAAO,6DAA6D,CAAC;YACvE,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;KACF,CAAC,CAAC;IAEH,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3D,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;IAClE,CAAC;AACH,CAAC;AAED,KAAK,UAAU,SAAS;IACtB,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAClE,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC3B,MAAM,YAAY,CAAC,QAAQ,EAAE,CAAC;QAChC,CAAC;QAED,OAAO,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED,KAAK,UAAU,qBAAqB,CAAC,OAAgC;IACnE,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;QAE1E,+CAA+C;QAC/C,IAAI,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAE5D,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,GAAG,gBAAgB,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,qHAAqH,EACrH,oBAAoB,CACrB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACd,IAAI,MAAM,KAAK,oBAAoB,EAAE,CAAC;oBACpC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QAC3D,CAAC;QAED,6BAA6B;QAC7B,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAC5E,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wCAAwC,CAAC,CAAC;YACzE,OAAO;QACT,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;QAElE,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,mDAAmD,CAAC,CAAC;YACpF,OAAO;QACT,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAE/E,0BAA0B;QAC1B,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAC9B;YACE,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;YAC9C,KAAK,EAAE,8BAA8B;YACrC,WAAW,EAAE,KAAK;SACnB,EACD,KAAK,EAAE,QAAQ,EAAE,EAAE;YACjB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC,CAAC;YAEzE,kBAAkB;YAClB,MAAM,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;YAE1C,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC9B,2EAA2E,CAC5E,CAAC;gBACF,OAAO;YACT,CAAC;YAED,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC,CAAC;YAEvE,oBAAoB;YACpB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAS,WAAW,EAAE,MAAM,CAAC,CAAC;YAC1D,MAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAS,cAAc,EAAE,EAAE,CAAC,CAAC;YAE5D,wEAAwE;YACxE,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACnD,IAAI,eAAe,GAAG,SAAS,EAAE,CAAC;gBAChC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACnD,oCAAoC,eAAe,gBAAgB,SAAS,KAAK;oBACjF,kDAAkD,EAClD,UAAU,EACV,QAAQ,CACT,CAAC;gBACF,IAAI,MAAM,KAAK,UAAU,EAAE,CAAC;oBAC1B,OAAO;gBACT,CAAC;YACH,CAAC;YAED,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;YAErE,0BAA0B;YAC1B,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YAEtE,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC,CAAC;YAE1E,gCAAgC;YAChC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,aAAa,CAAC;YAEpC,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YAEtD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wCAAwC,CAAC,CAAC;QACjF,CAAC,CACF,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;QAC9E,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;QAC9E,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,YAAY,CAAC,CAAC;QACnE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC5B,sCAAsC,YAAY,EAAE,CACrD,CAAC;IACJ,CAAC;AACH,CAAC;AAED,KAAK,UAAU,aAAa,CAC1B,MAAc,EACd,IAAY,EACZ,YAAoB;IAEpB,MAAM,UAAU,GAAG,YAAY;QAC7B,uEAAuE;YACvE,mDAAmD;YACnD,0CAA0C;YAC1C,iDAAiD,CAAC;IAEpD,MAAM,MAAM,GAAG,GAAG,UAAU,kBAAkB,IAAI,EAAE,CAAC;IAErD,MAAM,WAAW,GAAG;QAClB,QAAQ,EAAE,CAAC;gBACT,KAAK,EAAE,CAAC;wBACN,IAAI,EAAE,MAAM;qBACb,CAAC;aACH,CAAC;QACF,gBAAgB,EAAE;YAChB,WAAW,EAAE,GAAG;YAChB,IAAI,EAAE,EAAE;YACR,IAAI,EAAE,IAAI;YACV,eAAe,EAAE,GAAG;SACrB;KACF,CAAC;IAEF,MAAM,GAAG,GAAG,GAAG,oBAAoB,QAAQ,MAAM,EAAE,CAAC;IAEpD,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;YAChC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;SAClC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;YAClF,CAAC;YACD,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;YACxE,CAAC;YACD,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAChF,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,kCAAkC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAoB,CAAC;QAErD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/D,OAAO,aAAa,CAAC,IAAI,EAAE,CAAC;IAC9B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,MAAM,KAAK,CAAC;QACd,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;IACjE,CAAC;AACH,CAAC;AAED,SAAgB,UAAU;IACxB,oBAAoB;AACtB,CAAC"}