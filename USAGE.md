# How to Use the AI Commit Message Generator

## Quick Start Guide

**🚀 Ready to Use Immediately!**

The extension works out of the box with a built-in API key. No setup required!

### 1. Optional: Set Your Own API Key

For better performance and quota management, you can optionally set your own API key:

1. **Get a Gemini API Key** (Optional):
   - Visit [Google AI Studio](https://aistudio.google.com/)
   - Sign in with your Google account
   - Click "Get API Key" and create a new key
   - Copy the API key (it starts with `AIzaSy...`)

2. **Set the API Key in VS Code** (Optional):
   - Open the Command Palette (`Ctrl+Shift+P` on Windows/Linux, `Cmd+Shift+P` on Mac)
   - Type "Set Gemini API Key" and select the command
   - Paste your API key when prompted
   - The key will be stored securely in your system's credential manager

### 2. Generate Commit Messages

1. **Stage Your Changes**:
   - Make changes to your code
   - Stage the files you want to commit using:
     - Git command: `git add <files>`
     - VS Code Source Control panel: Click the "+" next to files

2. **Generate the Message**:
   - **Method 1**: Click the sparkle (✨) icon in the Source Control panel
   - **Method 2**: Open Command Palette and run "Generate AI Commit Message"

3. **Review and Commit**:
   - The AI-generated message will appear in the commit message box
   - Review the message and edit if needed
   - Commit your changes as usual

## Configuration Options

You can customize the extension through VS Code settings:

### Model Selection
- **Setting**: `ai-commit-generator.model`
- **Options**:
  - `gemini-2.5-flash-lite-preview-06-17` (default - fast and efficient)
  - `gemini-2.0-flash` (balanced performance)
  - `gemini-1.5-pro` (most capable, higher cost)

### Token Limit
- **Setting**: `ai-commit-generator.maxTokens`
- **Default**: 100,000
- **Description**: Maximum number of tokens to send to the API

### Custom Prompt
- **Setting**: `ai-commit-generator.customPrompt`
- **Default**: Empty
- **Description**: Custom instructions to add before the diff analysis

## Tips for Best Results

1. **Stage Meaningful Changes**: Only stage related changes together for more coherent commit messages

2. **Review Generated Messages**: Always review the AI-generated message before committing

3. **Use Conventional Commits**: The AI tries to follow conventional commit format when applicable

4. **Handle Large Diffs**: For very large changes, consider breaking them into smaller, logical commits

## Troubleshooting

### "No staged changes found"
- Make sure you've staged your changes with `git add` or through VS Code's Source Control panel

### "API rate limit exceeded"
- Wait a moment before trying again
- Consider using a less frequent generation pattern

### "Invalid API key"
- Verify your API key is correct
- Run "Set Gemini API Key" command to update it

### "Request too large"
- Try staging fewer files at once
- Break large changes into smaller commits

## Security Notes

- Your API key is stored securely using VS Code's SecretStorage API
- Keys are encrypted by your operating system's credential manager
- No API keys are stored in plain text files
- Each extension has its own secure storage space

## Example Workflow

```bash
# 1. Make changes to your code
echo "console.log('Hello World');" >> app.js

# 2. Stage the changes
git add app.js

# 3. In VS Code, click the sparkle icon or use Command Palette
# 4. Review the generated message like: "Add console log statement to app.js"
# 5. Commit as usual
git commit  # Message is already filled in
```

That's it! The extension will help you create consistent, meaningful commit messages automatically.