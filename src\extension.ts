import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
  console.log('🚀 Simple AI Commit Generator: Starting...');
  
  // Test command
  const testCommand = vscode.commands.registerCommand('ai-commit-generator.test', () => {
    vscode.window.showInformationMessage('✅ Extension is working!');
    console.log('✅ Test command works');
  });

  // Generate command - simplified
  const generateCommand = vscode.commands.registerCommand('ai-commit-generator.generate', async () => {
    console.log('🎯 Generate button clicked!');
    
    try {
      vscode.window.showInformationMessage('🤖 AI Commit Generator button works!');
      
      // Just set a simple message for now
      const gitExtension = vscode.extensions.getExtension('vscode.git');
      if (gitExtension && gitExtension.isActive) {
        const gitAPI = gitExtension.exports.getAPI(1);
        const repo = gitAPI.repositories[0];
        if (repo) {
          repo.inputBox.value = 'feat: AI generated commit message (test)';
          vscode.window.showInformationMessage('✅ Commit message set!');
        } else {
          vscode.window.showErrorMessage('❌ No Git repository found');
        }
      } else {
        vscode.window.showErrorMessage('❌ Git extension not available');
      }
      
    } catch (error) {
      console.error('❌ Error:', error);
      vscode.window.showErrorMessage(`❌ Error: ${error}`);
    }
  });

  context.subscriptions.push(testCommand, generateCommand);
  
  console.log('✅ Simple extension activated');
  vscode.window.showInformationMessage('🚀 Simple AI Commit Generator ready!');
}

export function deactivate() {
  console.log('👋 Extension deactivated');
}
