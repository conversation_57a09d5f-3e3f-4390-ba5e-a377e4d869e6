import * as vscode from 'vscode';
import * as simpleGit from 'simple-git';

// --- Configuration ---
// The hardcoded API key for the Gemini API.
const API_KEY = 'AIzaSyCMiod0mCRxOR2eIcl4fvXKppQpoXYNQ64';
// The default endpoint for the Gemini API.
const API_ENDPOINT = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent';

// --- Interfaces ---
/**
 * Defines the structure of the expected response from the Gemini API.
 */
interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
  error?: {
    message: string;
    code: number;
  };
}

// --- Extension Activation ---
/**
 * This function is called when the extension is activated.
 * It sets up the commands that the user can run.
 * @param context The extension context provided by VS Code.
 */
export function activate(context: vscode.ExtensionContext) {
  console.log('🚀 Simple AI Commit Generator: Starting...');

  // Register a simple test command to confirm the extension is running.
  const testCommand = vscode.commands.registerCommand('ai-commit-generator.test', () => {
    vscode.window.showInformationMessage('✅ Extension is working!');
    console.log('✅ Test command works');
  });

  // Register the main command to generate a commit message.
  const generateCommand = vscode.commands.registerCommand('ai-commit-generator.generate', async () => {
    console.log('🎯 Generate button clicked!');

    try {
      vscode.window.withProgress({
          location: vscode.ProgressLocation.SourceControl,
          title: "🤖 Generating AI commit message...",
          cancellable: false
      }, async (progress) => {
        // Get the official VS Code Git extension
        const gitExtension = vscode.extensions.getExtension('vscode.git');
        if (!gitExtension) {
          vscode.window.showErrorMessage('❌ Git extension not found. Please ensure it is enabled.');
          return;
        }

        // Activate the Git extension if it's not already active
        if (!gitExtension.isActive) {
          await gitExtension.activate();
        }

        const gitAPI = gitExtension.exports.getAPI(1);
        const repo = gitAPI.repositories[0];

        if (!repo) {
          vscode.window.showErrorMessage('❌ No Git repository found in the current workspace.');
          return;
        }

        // Use simple-git to get the staged changes
        const git = simpleGit.simpleGit(repo.rootUri.fsPath);
        const diff = await git.diff(['--staged']);

        if (!diff || diff.trim().length === 0) {
          vscode.window.showWarningMessage('⚠️ No staged changes found. Please stage your changes before generating a commit message.');
          return;
        }

        console.log('📝 Found staged changes, calling AI...');

        // Call the Gemini API with the diff
        const commitMessage = await callGeminiAPI(diff);

        // Populate the commit message box in the Source Control view
        repo.inputBox.value = commitMessage;

        vscode.window.showInformationMessage('✅ AI commit message generated!');
      });

    } catch (error) {
      console.error('❌ Error during commit generation:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.';
      vscode.window.showErrorMessage(`❌ Failed to generate commit: ${errorMessage}`);
    }
  });

  // Add the commands to the extension's subscriptions to ensure they are disposed on deactivation.
  context.subscriptions.push(testCommand, generateCommand);

  console.log('✅ AI Commit Generator activated');
  vscode.window.showInformationMessage('🚀 AI Commit Generator is ready!');
}

// --- API Call ---
/**
 * Calls the Gemini API to generate a commit message based on the provided git diff.
 * @param diff The string containing the staged git changes.
 * @returns A promise that resolves to the generated commit message as a string.
 */
async function callGeminiAPI(diff: string): Promise<string> {
  try {
    const prompt =
      'Generate a concise, clear commit message for the following git diff. ' +
      'Follow the conventional commit format (e.g., "feat: add new feature"). ' +
      'The message should focus on what changed and why, not how. ' +
      'The first line should be under 72 characters.\n\n' +
      `Git diff:\n${diff}`;

    const requestBody = {
      contents: [{
        parts: [{
          text: prompt
        }]
      }],
      generationConfig: {
        temperature: 0.4,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 250,
      }
    };

    const url = `${API_ENDPOINT}?key=${API_KEY}`;
    console.log('🌐 Calling Gemini API...');

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
        const errorBody = await response.text();
        console.error('❌ API request failed:', response.status, errorBody);
        throw new Error(`API request failed with status ${response.status}. Check the console for details.`);
    }

    const data = await response.json() as GeminiResponse;

    if (data.error) {
      throw new Error(`API Error: ${data.error.message}`);
    }

    if (!data.candidates || data.candidates.length === 0 || !data.candidates[0].content.parts[0].text) {
      throw new Error('No response or content generated by the API.');
    }

    const generatedText = data.candidates[0].content.parts[0].text;
    console.log('✅ AI response received.');
    return generatedText.trim();

  } catch (error) {
    console.error('❌ API call failed:', error);
    // Re-throw the error to be caught by the calling command handler.
    throw new Error(`Failed to generate commit message: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// --- Extension Deactivation ---
/**
 * This function is called when the extension is deactivated.
 */
export function deactivate() {
  console.log('👋 AI Commit Generator deactivated.');
}
