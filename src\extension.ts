import * as vscode from 'vscode';
import * as simpleGit from 'simple-git';

const API_KEY_SECRET = 'gemini-api-key';
const DEFAULT_API_ENDPOINT = 'https://generativelanguage.googleapis.com/v1beta/models';

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
  error?: {
    message: string;
    code: number;
  };
}

interface GitRepository {
  rootUri: vscode.Uri;
  inputBox: {
    value: string;
  };
}

interface GitAPI {
  repositories: GitRepository[];
}

export function activate(context: vscode.ExtensionContext) {
  // Register commands
  const generateCommand = vscode.commands.registerCommand(
    'ai-commit-generator.generate',
    () => generateCommitMessage(context)
  );

  const setApiKeyCommand = vscode.commands.registerCommand(
    'ai-commit-generator.setApiKey',
    () => setApiKey(context)
  );

  context.subscriptions.push(generateCommand, setApiKeyCommand);

  // Check if API key exists on activation
  checkApiKeyOnActivation(context);
}

async function checkApiKeyOnActivation(context: vscode.ExtensionContext) {
  const apiKey = await context.secrets.get(API_KEY_SECRET);
  if (!apiKey) {
    const action = await vscode.window.showInformationMessage(
      'Gemini API key not found. Would you like to set it now?',
      'Set API Key',
      'Later'
    );
    if (action === 'Set API Key') {
      await setApiKey(context);
    }
  }
}

async function setApiKey(context: vscode.ExtensionContext) {
  const apiKey = await vscode.window.showInputBox({
    prompt: 'Enter your Gemini API key',
    password: true,
    placeHolder: 'AIzaSy...',
    validateInput: (value) => {
      if (!value || value.trim().length === 0) {
        return 'API key cannot be empty';
      }
      if (!value.startsWith('AIzaSy')) {
        return 'Invalid API key format. Gemini API keys start with "AIzaSy"';
      }
      return null;
    }
  });

  if (apiKey) {
    await context.secrets.store(API_KEY_SECRET, apiKey.trim());
    vscode.window.showInformationMessage('API key saved securely!');
  }
}

async function getGitAPI(): Promise<GitAPI | null> {
  try {
    const gitExtension = vscode.extensions.getExtension('vscode.git');
    if (!gitExtension) {
      return null;
    }

    if (!gitExtension.isActive) {
      await gitExtension.activate();
    }

    return gitExtension.exports.getAPI(1);
  } catch (error) {
    console.error('Error getting Git API:', error);
    return null;
  }
}

async function generateCommitMessage(context: vscode.ExtensionContext) {
  try {
    // Check for API key
    const apiKey = await context.secrets.get(API_KEY_SECRET);
    if (!apiKey) {
      const action = await vscode.window.showErrorMessage(
        'Gemini API key not found. Please set your API key first.',
        'Set API Key'
      );
      if (action === 'Set API Key') {
        await setApiKey(context);
      }
      return;
    }

    // Get Git API and repository
    const gitAPI = await getGitAPI();
    if (!gitAPI) {
      vscode.window.showErrorMessage('Git extension not found or not active.');
      return;
    }

    const repo = gitAPI.repositories[0];
    if (!repo) {
      vscode.window.showErrorMessage('No Git repository found in the current workspace.');
      return;
    }

    // Show progress indicator
    await vscode.window.withProgress(
      {
        location: vscode.ProgressLocation.Notification,
        title: 'Generating commit message...',
        cancellable: false
      },
      async (progress) => {
        progress.report({ increment: 20, message: 'Getting staged changes...' });

        // Get staged diff
        const git = simpleGit.simpleGit(repo.rootUri.fsPath);
        const diff = await git.diff(['--staged']);

        if (!diff || diff.trim().length === 0) {
          vscode.window.showWarningMessage(
            'No staged changes found. Please stage your changes first using "git add".'
          );
          return;
        }

        progress.report({ increment: 40, message: 'Validating diff size...' });

        // Get configuration
        const config = vscode.workspace.getConfiguration('ai-commit-generator');
        const model = config.get<string>('model', 'gemini-2.5-flash-lite-preview-06-17');
        const maxTokens = config.get<number>('maxTokens', 100000);
        const customPrompt = config.get<string>('customPrompt', '');

        // Simple token estimation (rough approximation: 1 token ≈ 4 characters)
        const estimatedTokens = Math.ceil(diff.length / 4);
        if (estimatedTokens > maxTokens) {
          const action = await vscode.window.showWarningMessage(
            `The diff is too large (estimated ${estimatedTokens} tokens, max ${maxTokens}). ` +
            'This may exceed the API limits. Continue anyway?',
            'Continue',
            'Cancel'
          );
          if (action !== 'Continue') {
            return;
          }
        }

        progress.report({ increment: 60, message: 'Calling Gemini API...' });

        // Generate commit message
        const commitMessage = await callGeminiAPI(apiKey, model, diff, customPrompt);
        
        progress.report({ increment: 90, message: 'Updating commit message...' });

        // Update the commit message box
        repo.inputBox.value = commitMessage;
        
        progress.report({ increment: 100, message: 'Done!' });
        
        vscode.window.showInformationMessage('Commit message generated successfully!');
      }
    );
  } catch (error) {
    console.error('Error generating commit message:', error);
    vscode.window.showErrorMessage(
      `Failed to generate commit message: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

async function callGeminiAPI(
  apiKey: string,
  model: string,
  diff: string,
  customPrompt: string
): Promise<string> {
  const basePrompt = customPrompt || 
    'Generate a concise, clear commit message for the following git diff. ' +
    'Follow conventional commit format if applicable. ' +
    'Focus on what changed and why, not how. ' +
    'Keep it under 72 characters for the first line.';

  const prompt = `${basePrompt}\n\nGit diff:\n${diff}`;

  const requestBody = {
    contents: [{
      parts: [{
        text: prompt
      }]
    }],
    generationConfig: {
      temperature: 0.3,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: 200,
    }
  };

  const url = `${DEFAULT_API_ENDPOINT}/${model}:generateContent?key=${apiKey}`;

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      if (response.status === 429) {
        throw new Error('API rate limit exceeded. Please wait a moment and try again.');
      }
      if (response.status === 401) {
        throw new Error('Invalid API key. Please check your Gemini API key.');
      }
      if (response.status === 400) {
        throw new Error('Request too large or malformed. Try staging fewer changes.');
      }
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json() as GeminiResponse;

    if (data.error) {
      throw new Error(`API Error: ${data.error.message}`);
    }

    if (!data.candidates || data.candidates.length === 0) {
      throw new Error('No response generated by the API');
    }

    const generatedText = data.candidates[0].content.parts[0].text;
    return generatedText.trim();
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Network error or unexpected response format');
  }
}

export function deactivate() {
  // Cleanup if needed
}