{"version": 3, "file": "extension-simple.js", "sourceRoot": "", "sources": ["../src/extension-simple.ts"], "names": [], "mappings": ";;AAEA,4BAyCC;AAED,gCAEC;AA/CD,iCAAiC;AAEjC,SAAgB,QAAQ,CAAC,OAAgC;IACvD,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE1D,eAAe;IACf,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACnF,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,gCAAgC;IAChC,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;QACjG,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAE3C,IAAI,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,sCAAsC,CAAC,CAAC;YAE7E,oCAAoC;YACpC,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAClE,IAAI,YAAY,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC1C,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC9C,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACpC,IAAI,IAAI,EAAE,CAAC;oBACT,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,0CAA0C,CAAC;oBACjE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,CAAC;gBAChE,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;YAClE,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,KAAK,EAAE,CAAC,CAAC;QACtD,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;IAEzD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAC5C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,sCAAsC,CAAC,CAAC;AAC/E,CAAC;AAED,SAAgB,UAAU;IACxB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;AAC1C,CAAC"}