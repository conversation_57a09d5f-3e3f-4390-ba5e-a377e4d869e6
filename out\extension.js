"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = require("vscode");
const simpleGit = require("simple-git");
const API_KEY_SECRET = 'gemini-AIzaSyCMiod0mCRxOR2eIcl4fvXKppQpoXYNQ64-key';
const DEFAULT_API_ENDPOINT = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent';
const FALLBACK_API_KEY = 'AIzaSyCMiod0mCRxOR2eIcl4fvXKppQpoXYNQ64';
function activate(context) {
    console.log('AI Commit Generator: Extension is being activated...');
    // Register commands
    const generateCommand = vscode.commands.registerCommand('ai-commit-generator.generate', () => {
        console.log('AI Commit Generator: Generate command triggered');
        return generateCommitMessage(context);
    });
    const setApiKeyCommand = vscode.commands.registerCommand('ai-commit-generator.setApiKey', () => {
        console.log('AI Commit Generator: Set API key command triggered');
        return setApiKey(context);
    });
    context.subscriptions.push(generateCommand, setApiKeyCommand);
    console.log('AI Commit Generator: Commands registered successfully');
    // Check if API key exists on activation
    checkApiKeyOnActivation(context);
    console.log('AI Commit Generator: Extension activated successfully');
}
async function checkApiKeyOnActivation(context) {
    const apiKey = await context.secrets.get(API_KEY_SECRET);
    if (!apiKey) {
        const action = await vscode.window.showInformationMessage('No custom Gemini API key found. The extension will use a default key, but you can set your own for better performance and quota management.', 'Set My Own API Key', 'Use Default Key');
        if (action === 'Set My Own API Key') {
            await setApiKey(context);
        }
    }
}
async function setApiKey(context) {
    const apiKey = await vscode.window.showInputBox({
        prompt: 'Enter your Gemini API key',
        password: true,
        placeHolder: 'AIzaSy...',
        validateInput: (value) => {
            if (!value || value.trim().length === 0) {
                return 'API key cannot be empty';
            }
            if (!value.startsWith('AIzaSy')) {
                return 'Invalid API key format. Gemini API keys start with "AIzaSy"';
            }
            return null;
        }
    });
    if (apiKey) {
        await context.secrets.store(API_KEY_SECRET, apiKey.trim());
        vscode.window.showInformationMessage('API key saved securely!');
    }
}
async function getGitAPI() {
    try {
        const gitExtension = vscode.extensions.getExtension('vscode.git');
        if (!gitExtension) {
            return null;
        }
        if (!gitExtension.isActive) {
            await gitExtension.activate();
        }
        return gitExtension.exports.getAPI(1);
    }
    catch (error) {
        console.error('Error getting Git API:', error);
        return null;
    }
}
async function generateCommitMessage(context) {
    try {
        console.log('AI Commit Generator: Starting commit message generation...');
        // Check for API key - use fallback if none set
        let apiKey = await context.secrets.get(API_KEY_SECRET);
        console.log('AI Commit Generator: API key check completed');
        if (!apiKey) {
            apiKey = FALLBACK_API_KEY;
            console.log('AI Commit Generator: Using fallback API key');
            vscode.window.showInformationMessage('Using default API key. You can set your own API key using "Set Gemini API Key" command for better quota management.', 'Set My Own API Key').then(action => {
                if (action === 'Set My Own API Key') {
                    setApiKey(context);
                }
            });
        }
        else {
            console.log('AI Commit Generator: Using stored API key');
        }
        // Get Git API and repository
        console.log('AI Commit Generator: Getting Git API...');
        const gitAPI = await getGitAPI();
        if (!gitAPI) {
            console.error('AI Commit Generator: Git extension not found or not active');
            vscode.window.showErrorMessage('Git extension not found or not active.');
            return;
        }
        console.log('AI Commit Generator: Git API obtained successfully');
        const repo = gitAPI.repositories[0];
        if (!repo) {
            console.error('AI Commit Generator: No Git repository found');
            vscode.window.showErrorMessage('No Git repository found in the current workspace.');
            return;
        }
        console.log('AI Commit Generator: Git repository found:', repo.rootUri.fsPath);
        // Show progress indicator
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Generating commit message...',
            cancellable: false
        }, async (progress) => {
            progress.report({ increment: 20, message: 'Getting staged changes...' });
            // Get staged diff
            const git = simpleGit.simpleGit(repo.rootUri.fsPath);
            const diff = await git.diff(['--staged']);
            if (!diff || diff.trim().length === 0) {
                vscode.window.showWarningMessage('No staged changes found. Please stage your changes first using "git add".');
                return;
            }
            progress.report({ increment: 40, message: 'Validating diff size...' });
            // Get configuration
            const config = vscode.workspace.getConfiguration('ai-commit-generator');
            const maxTokens = config.get('maxTokens', 100000);
            const customPrompt = config.get('customPrompt', '');
            // Simple token estimation (rough approximation: 1 token ≈ 4 characters)
            const estimatedTokens = Math.ceil(diff.length / 4);
            if (estimatedTokens > maxTokens) {
                const action = await vscode.window.showWarningMessage(`The diff is too large (estimated ${estimatedTokens} tokens, max ${maxTokens}). ` +
                    'This may exceed the API limits. Continue anyway?', 'Continue', 'Cancel');
                if (action !== 'Continue') {
                    return;
                }
            }
            progress.report({ increment: 60, message: 'Calling Gemini API...' });
            // Generate commit message
            const commitMessage = await callGeminiAPI(apiKey, diff, customPrompt);
            progress.report({ increment: 90, message: 'Updating commit message...' });
            // Update the commit message box
            repo.inputBox.value = commitMessage;
            progress.report({ increment: 100, message: 'Done!' });
            vscode.window.showInformationMessage('Commit message generated successfully!');
        });
    }
    catch (error) {
        console.error('AI Commit Generator: Error generating commit message:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('AI Commit Generator: Error details:', errorMessage);
        vscode.window.showErrorMessage(`Failed to generate commit message: ${errorMessage}`);
    }
}
async function callGeminiAPI(apiKey, diff, customPrompt) {
    const basePrompt = customPrompt ||
        'Generate a concise, clear commit message for the following git diff. ' +
            'Follow conventional commit format if applicable. ' +
            'Focus on what changed and why, not how. ' +
            'Keep it under 72 characters for the first line.';
    const prompt = `${basePrompt}\n\nGit diff:\n${diff}`;
    const requestBody = {
        contents: [{
                parts: [{
                        text: prompt
                    }]
            }],
        generationConfig: {
            temperature: 0.3,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 200,
        }
    };
    const url = `${DEFAULT_API_ENDPOINT}?key=${apiKey}`;
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });
        if (!response.ok) {
            if (response.status === 429) {
                throw new Error('API rate limit exceeded. Please wait a moment and try again.');
            }
            if (response.status === 401) {
                throw new Error('Invalid API key. Please check your Gemini API key.');
            }
            if (response.status === 400) {
                throw new Error('Request too large or malformed. Try staging fewer changes.');
            }
            throw new Error(`API request failed with status ${response.status}`);
        }
        const data = await response.json();
        if (data.error) {
            throw new Error(`API Error: ${data.error.message}`);
        }
        if (!data.candidates || data.candidates.length === 0) {
            throw new Error('No response generated by the API');
        }
        const generatedText = data.candidates[0].content.parts[0].text;
        return generatedText.trim();
    }
    catch (error) {
        if (error instanceof Error) {
            throw error;
        }
        throw new Error('Network error or unexpected response format');
    }
}
function deactivate() {
    // Cleanup if needed
}
//# sourceMappingURL=extension.js.map