{"version": 3, "file": "extension-backup.js", "sourceRoot": "", "sources": ["../src/extension-backup.ts"], "names": [], "mappings": ";;AAuBA,4BAsEC;AAqGD,gCAEC;AApMD,iCAAiC;AACjC,wCAAwC;AAExC,MAAM,cAAc,GAAG,gBAAgB,CAAC;AACxC,MAAM,oBAAoB,GAAG,6GAA6G,CAAC;AAC3I,MAAM,gBAAgB,GAAG,yCAAyC,CAAC;AAkBnE,SAAgB,QAAQ,CAAC,OAAgC;IACvD,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAE9D,4BAA4B;IAC5B,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACnF,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,mCAAmC,CAAC,CAAC;QAC1E,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;QACjG,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAE7C,IAAI,CAAC;YACH,0CAA0C;YAC1C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,iCAAiC,CAAC,CAAC;YAExE,wBAAwB;YACxB,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAClE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;gBAC5D,OAAO;YACT,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC3B,MAAM,YAAY,CAAC,QAAQ,EAAE,CAAC;YAChC,CAAC;YAED,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEpC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;gBAC5D,OAAO;YACT,CAAC;YAED,qBAAqB;YACrB,MAAM,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;YAE1C,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,8DAA8D,CAAC,CAAC;gBACjG,OAAO;YACT,CAAC;YAED,WAAW;YACX,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,gBAAgB,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YAEtE,yBAAyB;YACzB,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,aAAa,CAAC;YAEpC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6BAA6B,CAAC,CAAC;QAEtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACjC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACzG,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,kBAAkB;IAClB,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QACnG,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,eAAe,EAAE,gBAAgB,CAAC,CAAC;IAE3E,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;IACvE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;AAC3E,CAAC;AAID,KAAK,UAAU,SAAS,CAAC,OAAgC;IACvD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;QAC9C,MAAM,EAAE,2BAA2B;QACnC,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,WAAW;QACxB,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE;YACvB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxC,OAAO,yBAAyB,CAAC;YACnC,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChC,OAAO,6DAA6D,CAAC;YACvE,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;KACF,CAAC,CAAC;IAEH,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3D,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;IAClE,CAAC;AACH,CAAC;AAMD,KAAK,UAAU,aAAa,CAC1B,MAAc,EACd,IAAY,EACZ,YAAoB;IAEpB,MAAM,UAAU,GAAG,YAAY;QAC7B,uEAAuE;YACvE,mDAAmD;YACnD,0CAA0C;YAC1C,iDAAiD,CAAC;IAEpD,MAAM,MAAM,GAAG,GAAG,UAAU,kBAAkB,IAAI,EAAE,CAAC;IAErD,MAAM,WAAW,GAAG;QAClB,QAAQ,EAAE,CAAC;gBACT,KAAK,EAAE,CAAC;wBACN,IAAI,EAAE,MAAM;qBACb,CAAC;aACH,CAAC;QACF,gBAAgB,EAAE;YAChB,WAAW,EAAE,GAAG;YAChB,IAAI,EAAE,EAAE;YACR,IAAI,EAAE,IAAI;YACV,eAAe,EAAE,GAAG;SACrB;KACF,CAAC;IAEF,MAAM,GAAG,GAAG,GAAG,oBAAoB,QAAQ,MAAM,EAAE,CAAC;IAEpD,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;YAChC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;SAClC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;YAClF,CAAC;YACD,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;YACxE,CAAC;YACD,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAChF,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,kCAAkC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAoB,CAAC;QAErD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC/D,OAAO,aAAa,CAAC,IAAI,EAAE,CAAC;IAC9B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,MAAM,KAAK,CAAC;QACd,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;IACjE,CAAC;AACH,CAAC;AAED,SAAgB,UAAU;IACxB,oBAAoB;AACtB,CAAC"}