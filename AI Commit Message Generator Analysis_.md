# **Building a Production-Grade AI Commit Message Generator for VS Code**

### **Introduction**

This report provides an exhaustive technical analysis, architectural critique, and a set of production-grade recommendations for building an AI-powered commit message generator as a Visual Studio Code extension. The analysis uses a publicly available tutorial for creating a prototype with the Google Gemini API as a baseline, deconstructing its implementation to identify areas of improvement concerning security, robustness, and user experience.

The methodology for this report is grounded in a comprehensive review of the official VS Code Extension API documentation, established best practices in software security for handling sensitive credentials, and the technical specifications of the Google Gemini API and its associated dependencies.

The core findings indicate that while the baseline prototype is functionally sound for a demonstration, it harbors significant security vulnerabilities in its handling of API keys and lacks the necessary robustness for real-world use. Key deficiencies include a fragile interaction with the external API, inadequate error handling, and an architecture that could be improved for better maintainability and a more refined user experience.

This report is structured to guide the reader from an initial analysis of the prototype to a final, recommended implementation. It begins by deconstructing the prototype's manifest and core logic, then performs a deep-dive feasibility review of its architectural components. Following a critical security assessment, the report culminates in a complete, recommended architecture with full source code, designed to be secure, robust, and user-friendly.

## **Section 1: Analysis of the Prototype Implementation**

This section performs a close reading of the provided tutorial code, evaluating its functional correctness and identifying immediate areas for deeper analysis. The prototype serves as a valuable starting point but exhibits common traits of a proof-of-concept that must be addressed before it can be considered a production-ready tool.

### **1.1. Extension Manifest (package.json) Deconstruction**

The package.json file serves as the extension's manifest, declaring its capabilities and integration points with the VS Code UI. The prototype's manifest is largely correct but makes a critical misstep in its approach to configuration.

#### **Contribution Points Analysis**

The contributes section of the manifest is where an extension statically declares how it plugs into VS Code.1

* **commands**: The registration of the ai-commit-generator.generate command is standard and effective. It defines a unique identifier for the action, a user-friendly title for the Command Palette, and a sparkle icon for visual representation in menus.2  
* **menus**: The manifest strategically places the command's icon in the Source Control Management (SCM) view's title bar using the scm/title menu contribution point. This is an excellent choice for discoverability and workflow integration. The when: "scmProvider \== 'git'" clause correctly ensures the button only appears when the active SCM provider is Git, preventing UI clutter in non-Git projects.4 Placing it in the  
  navigation group ensures it appears as a primary, inline action icon.3  
* **configuration**: This is the most significant point of analysis and the manifest's primary flaw. The manifest correctly defines a configuration property, ai-commit-generator.geminiApiKey, which creates an input field in the VS Code settings UI for the user's API key.2 While functionally simple, using VS Code's standard configuration system for storing secrets is a severe security anti-pattern, a topic explored in detail in Section 3 of this report.

#### **Activation Events Analysis**

The manifest uses "activationEvents": \["onCommand:ai-commit-generator.generate"\] to control when the extension's code is loaded and executed. This is a highly efficient approach, ensuring the extension remains dormant and consumes zero system resources until a user explicitly invokes the command.1 This "lazy loading" is a fundamental best practice for VS Code extension performance. While newer versions of VS Code can often infer this activation event from the

commands contribution, explicitly declaring it is robust and ensures compatibility.1

### **1.2. Activation and Core Command Logic (extension.ts)**

The core logic in extension.ts implements the functionality promised by the manifest. The overall flow is logical, but its implementation details reveal brittleness.

* **Git API Acquisition (getGitAPI)**: The helper function to acquire the API from the built-in vscode.git extension is well-implemented. It correctly checks for the extension's existence and ensures it is active before attempting to access its exports. This pattern aligns with official guidance for inter-extension communication.6  
* **Command Registration and Execution Flow**: The command's execution flow follows a clear, four-step process:  
  1. **API Key Retrieval**: The code correctly uses vscode.workspace.getConfiguration to read the setting defined in package.json. The check for a missing key is a necessary validation step.  
  2. **Git Diff Retrieval**: The logic correctly identifies the active repository and initializes the simple-git library. The call to await git.diff(\['--staged'\]) successfully retrieves the text diff of all staged changes.8 The subsequent check for an empty diff prevents unnecessary API calls.  
  3. **API Call**: The code constructs a prompt and uses fetch to call the Gemini API. The try...catch block is essential for handling network failures or other runtime errors during the API request.  
  4. **UI Update**: The use of repo.inputBox.value \= commitMessage; is the correct and most direct method for populating the SCM commit message box, as documented in the SCM API.4

This implementation reveals an architectural inconsistency. The code leverages the native vscode.git API to get the repository's path and to update the commit input box, but it switches to an external dependency, simple-git, for the crucial task of retrieving the diff. This hybrid approach suggests a potential limitation or complexity in the native API; indeed, the native API is noted for being poorly documented and may not offer a straightforward method for obtaining a raw diff string.7 While pragmatic, this reliance on two different methods for Git interaction adds a dependency and complexity that should be consciously evaluated.

Furthermore, the interaction with the Gemini API is fragile. The model name (gemini-2.0-flash) and API endpoint are hardcoded, preventing users from leveraging different models or adapting to future API changes.9 The prompt is static, offering no user customization. Most critically, the implementation completely overlooks the Gemini API's input token limits.10 A large

git diff, which can easily occur when staging many files or changes to large text files, will exceed the model's context window. This will cause the API to reject the request with a 400 Bad Request error, which the generic catch block will not handle gracefully, leaving the user with a cryptic failure message. This oversight renders the prototype unreliable for anything beyond trivial commits.

## **Section 2: Architectural Component Review and Feasibility**

This section performs a deep dive into each major component of the architecture, evaluating the chosen technologies against alternatives and assessing their suitability for a production environment.

### **2.1. Interfacing with VS Code's Source Control**

The prototype's method for interacting with VS Code's SCM features is fundamentally sound and leverages the correct APIs.

* **Analysis of vscode.git API**: The primary entry point is acquiring the Git extension's API via vscode.extensions.getExtension('vscode.git').exports.getAPI(1). This is the officially sanctioned method.6 The returned  
  API object's most important property is repositories, an array of Repository objects, one for each Git repository in the user's workspace.7 The prototype correctly accesses  
  repositories, but a production-ready version must account for multi-root workspaces where this array could contain multiple repositories.  
* **Key API Objects**: The Repository object is the hub for interaction. The prototype correctly uses repository.rootUri to get the filesystem path for simple-git and repository.inputBox to access the commit message field.4 The  
  repository.state property, which the prototype does not use, provides rich, real-time information about the repository, such as the current HEAD (branch), remotes, and references, which could be used for more advanced prompt engineering (e.g., including the branch name in the prompt).12  
* **Interacting with the SCM Input Box**: The line repo.inputBox.value \= commitMessage is the most direct and effective way to update the commit message. The SCM API was explicitly designed to expose this property for programmatic control.4

### **2.2. Git Data Retrieval: An Evaluation of simple-git and Its Alternatives**

The choice of library for retrieving the staged diff is a critical architectural decision with trade-offs in simplicity, security, and dependencies.

* **Critique of the Prototype's Approach**: The use of simple-git is a pragmatic and common choice. It provides a lightweight wrapper around the Git command-line interface (CLI), making it easy to execute commands like git diff \--staged.8  
* **Limitations and Risks of simple-git**:  
  * **System Dependency**: The library requires that the git executable is installed on the user's machine and available in the system's PATH.8 This is a reasonable assumption for a Git-focused extension but remains an external dependency outside the extension's control.  
  * **Security**: Because simple-git spawns a child process to run the git command, it can be a vector for command injection vulnerabilities if not used carefully. The prototype is safe in this regard as it does not pass any unsanitized user input to the command. However, it is crucial to keep the library updated, as older versions have had documented vulnerabilities.14  
  * **Parsing Brittleness**: The library works by parsing the string output of the Git CLI. This can be unreliable if the user has configured their Git installation to use a non-English locale, as the output may not match what the library expects.8  
* **Analysis of Alternatives**:  
  * **nodegit**: This library provides native Node.js bindings for libgit2, a C library implementation of Git. It does not spawn child processes, making it inherently more secure and often more performant.15 However, its API is significantly more complex. Retrieving a staged diff would require a multi-step process of fetching the  
    HEAD commit, getting its tree object, getting the index, and then performing a Diff.treeToIndex operation.17 This adds considerable development overhead for a simple task.  
  * **isomorphic-git**: This is a pure JavaScript implementation of the Git protocol. Its primary advantage is having zero external dependencies—it does not require the git executable to be installed and can even run in a browser.16 It works by directly reading and writing to the  
    .git directory. Obtaining a diff would involve using a function like statusMatrix to compare the state of files in the HEAD, working directory, and stage.20 While powerful, this is likely overkill for the extension's narrow requirements.

#### **Table 1: Comparative Analysis of Node.js Git Libraries**

| Feature | simple-git | nodegit | isomorphic-git |
| :---- | :---- | :---- | :---- |
| **Core Mechanism** | Wraps the git command-line executable. | Native Node.js bindings for the libgit2 C library. | Pure JavaScript implementation of the Git protocol. |
| **System Dependencies** | Requires git to be installed and in the PATH. | None (ships with its own binaries). | None (requires only Node.js). |
| **Security Model** | Spawns child processes; potential for command injection if not used carefully. | No child processes; generally more secure. | No child processes; operates on the filesystem. |
| **API Complexity** | High-level, mirrors Git commands. Very simple. | Low-level, object-oriented, powerful but complex. | High-level, promise-based, designed for JS environments. |
| **Best For** | Simple scripting, automation, and extensions where git is a guaranteed dependency. | Performance-critical applications or tools requiring deep, complex Git manipulation. | Cross-platform applications (Node/browser) and environments where installing git is not possible. |

**Recommendation**: For this extension's specific needs, continuing to use **simple-git** represents the best balance of simplicity and functionality. The security risks are manageable since no user input is passed to the command, and the development effort is minimal. It is imperative, however, to use the latest version and employ automated dependency scanning to mitigate future vulnerabilities.

### **2.3. API Communication: node-fetch vs. Native Fetch**

The prototype uses the node-fetch package to make HTTP requests. While this has long been the standard, the Node.js landscape has evolved.

Modern versions of Node.js (v18 and higher) now include a stable, globally available fetch implementation based on the high-performance Undici library.21 This eliminates the need for external packages like

node-fetch.

An extension's package.json file includes an engines.vscode field, which specifies the minimum version of VS Code the extension is compatible with.1 Because the version of VS Code dictates the version of Node.js its extension host runs on, setting a modern engine requirement (e.g.,

"engines": { "vscode": "^1.85.0" }) guarantees that a native, stable fetch API will be available.

This allows for a significant architectural simplification. By removing the node-fetch dependency, the extension's installation footprint is reduced, the node\_modules directory is simplified, and a potential source of third-party bugs or security issues is eliminated. This is a clear and recommended improvement.

### **2.4. Gemini API Integration: Model Selection, Token Limits, and Rate Limiting**

The prototype's integration with the Gemini API is functional but lacks the robustness required for a reliable tool.

* **Model Selection**: The API offers a variety of models, each with different capabilities, context window sizes, and pricing.9 Hardcoding a single model like  
  gemini-2.0-flash is inflexible. A production-grade tool should expose this as a user configuration, allowing users to choose between a fast, cheap model for daily use or a more powerful, expensive model for complex changes.  
* **Token Limits**: This is the most critical flaw in the prototype's API logic. The git diff output can vary dramatically in size. The Gemini API enforces a strict maximum token limit on its input.10 Sending a diff that exceeds this limit will result in a  
  400 Bad Request error. A robust implementation **must** validate the payload size *before* making the API call. The official Gemini SDKs provide a countTokens utility for this exact purpose.11 If the token count is too high, the extension should gracefully handle the situation by warning the user and aborting, or by truncating the diff.  
* **Rate Limiting**: The Gemini API also imposes rate limits, such as Requests Per Minute (RPM) and Tokens Per Minute (TPM).23 If a user triggers the command too frequently, the API will respond with a  
  429 Too Many Requests error. The prototype's generic catch block will not provide a meaningful error message. The implementation should specifically check for the 429 status code and display a clear, actionable message to the user (e.g., "API rate limit exceeded. Please wait a moment and try again.").

#### **Table 2: Gemini Model Comparison for Text Generation**

| Model ID | Max Input Tokens | Max Output Tokens | Paid Tier Price (Input, per 1M tokens) | Key Characteristics |  |
| :---- | :---- | :---- | :---- | :---- | :---- |
| gemini-2.0-flash | 1,048,576 | 8,192 | $0.10 (text) | Fast, cost-effective for a wide range of tasks. Good default choice. |  |
| gemini-2.5-flash | 1,048,576 | 8,192 | $0.30 (text) | Newer, more capable Flash model. Balances speed and intelligence. |  |
| gemini-1.5-pro | 1,048,576 | 8,192 | $1.25 (\<=128k), $2.50 (\>128k) | Highly capable for complex reasoning. Higher cost. |  |
| gemini-2.5-pro | 1,048,576 | 65,535 | $1.25 (\<=200k), $2.50 (\>200k) | State-of-the-art, large output capacity. Best for very detailed generation. |  |
| Data synthesized from sources.9 |  |  |  |  |  |

## **Section 3: A Critical Security Review of API Key Management**

The most severe flaw in the prototype is its method for storing the user's Gemini API key. This section details the risks of the current approach and outlines the correct, secure implementation.

### **3.1. The Inherent Risk of Storing Secrets in Configuration**

The prototype uses the contributes.configuration point in package.json to create a setting for the API key. This stores the key in a plaintext settings.json file. This is a well-established security anti-pattern.25

The risk manifests in two ways, depending on where the user saves the setting:

* **Workspace settings.json**: If the user saves the key at the workspace level, it is written to a file at .vscode/settings.json within the project's root directory. This file is frequently and intentionally committed to version control to share project-specific editor settings (e.g., formatting rules, linter configurations) among team members. A user who unthinkingly saves their personal API key here and pushes to a shared or public repository has effectively leaked their credential to anyone with access to that repository.25  
* **User settings.json**: If the user saves the key at the user level, it is stored in a global, OS-specific location outside the project directory. While this avoids accidental commits, the key is still stored in a plaintext JSON file on the disk. Any process running with the user's permissions, including other VS Code extensions, could potentially read this file.26 This demonstrates that security through obscurity is not sufficient protection for sensitive data.

### **3.2. The Correct Approach: Implementing the SecretStorage API**

VS Code provides a purpose-built API for handling sensitive data: the SecretStorage API.25 This should be considered the

**only** acceptable method for storing user-provided secrets like API keys.

The SecretStorage API is not a simple file store; it is a secure wrapper that delegates the actual storage and encryption to the host operating system's native credential management system 26:

* **macOS**: Keychain Access  
* **Windows**: Credential Manager  
* **Linux**: libsecret (GNOME Keyring, KWallet, etc.)

This approach provides two layers of security. First, the operating system handles the encryption-at-rest, leveraging robust, system-level security. Second, VS Code provides runtime access control. Each extension is given a unique ExtensionContext object upon activation. The context.secrets property is an instance of SecretStorage that is sandboxed to that specific extension.26 This means that one extension cannot read the secrets stored by another. This sandboxing is the core security guarantee that prevents a malicious or compromised extension from stealing credentials stored by other, legitimate extensions. This combination of OS-level encryption and extension-level sandboxing provides a highly secure and usable solution.

### **3.3. A Secure Workflow for API Key Provisioning and Retrieval**

Migrating from configuration to SecretStorage requires a change in the user workflow, moving from a settings page to a command-driven interaction.

1. **Remove from Configuration**: The ai-commit-generator.geminiApiKey property must be removed entirely from the contributes.configuration section of package.json.  
2. **Check for Key on Activation**: When the generate command is run, the first step should be to attempt to retrieve the key from secure storage: const apiKey \= await context.secrets.get('geminiApiKey');.  
3. **Prompt if Missing**: If the returned value is undefined, the extension must prompt the user to enter their key. This should be done using vscode.window.showInputBox, with the password: true option to mask the input for security: const userProvidedKey \= await vscode.window.showInputBox({ password: true, placeHolder: 'Enter your Gemini API Key', ignoreFocusOut: true });.  
4. **Store the Key**: If the user provides a key, it must be stored securely for future use: await context.secrets.store('geminiApiKey', userProvidedKey);.29  
5. **Provide Management Commands**: Good practice dictates providing separate commands for managing the key's lifecycle. An ai-commit-generator.setApiKey command would allow a user to update their key, and a ai-commit-generator.clearApiKey command would allow them to remove it. This provides a much better user experience than requiring re-installation to clear a stored secret.

## **Section 4: Recommendations for a Production-Grade Architecture**

This final section synthesizes all previous analysis into a complete, recommended architecture for the AI Commit Message Generator. It provides a refined manifest, the full source code for a robust implementation, and further recommendations for enhancing the user experience.

### **4.1. Refined Extension Manifest and User-Facing Commands**

The package.json should be updated to reflect a more secure and configurable architecture.

* **engines.vscode**: Set to a modern version (e.g., "^1.85.0") to ensure the availability of the native fetch API and other modern extension features.  
* **extensionDependencies**: Add "vscode.git" to the manifest. This explicitly declares a dependency on the built-in Git extension, ensuring it is activated before our extension attempts to use its API.6  
* **contributes.configuration**: The API key setting must be removed. In its place, new settings for user-facing options should be added:  
  * A setting for model selection, ideally an enum to provide a dropdown list of supported Gemini models (e.g., the ones listed in Table 2).  
  * A string setting for a promptTemplate, allowing advanced users to customize the prompt sent to the AI.  
* **contributes.commands**: In addition to the main generate command, new commands for API key management should be added:  
  * ai-commit-generator.setApiKey with the title "Set/Update Gemini API Key".  
  * ai-commit-generator.clearApiKey with the title "Clear Stored Gemini API Key".

### **4.2. The Complete, Recommended extension.ts Implementation**

The following TypeScript code represents a production-grade implementation. It is structured into logical classes for maintainability and incorporates all the recommendations from this report: secure key storage, robust API interaction, user configuration, and detailed error handling.

TypeScript

// src/extension.ts  
import \* as vscode from 'vscode';  
import { simpleGit, SimpleGit } from 'simple-git';

// A dedicated class for managing secrets using the SecretStorage API.  
class SecretManager {  
    constructor(private secretStorage: vscode.SecretStorage) {}

    async getApiKey(): Promise\<string | undefined\> {  
        return await this.secretStorage.get('geminiApiKey');  
    }

    async setApiKey(token?: string): Promise\<void\> {  
        if (token) {  
            await this.secretStorage.store('geminiApiKey', token);  
            vscode.window.showInformationMessage('Gemini API Key stored successfully.');  
        }  
    }

    async clearApiKey(): Promise\<void\> {  
        await this.secretStorage.delete('geminiApiKey');  
        vscode.window.showInformationMessage('Gemini API Key cleared.');  
    }  
}

// A client for interacting with the Gemini API.  
class GeminiAPIClient {  
    private apiKey: string;  
    private model: string;

    constructor(apiKey: string) {  
        this.apiKey \= apiKey;  
        const config \= vscode.workspace.getConfiguration('ai-commit-generator');  
        // Default to a fast, cost-effective model if not configured.  
        this.model \= config.get\<string\>('model') |

| 'gemini-2.0-flash';  
    }

    // A simple heuristic for token counting. 1 token \~= 4 chars.  
    // For a production app, a more precise tokenizer library would be better.  
    private estimateTokenCount(text: string): number {  
        return Math.ceil(text.length / 4);  
    }

    public async generateCommitMessage(diff: string): Promise\<string\> {  
        const config \= vscode.workspace.getConfiguration('ai-commit-generator');  
        const promptTemplate \= config.get\<string\>('promptTemplate') |

|   
            \`Based on the following git diff, generate a concise and descriptive commit message. Follow the conventional commit format (e.g., "feat: add new feature"). The diff is:\\n\\n\\\`\\\`\\\`diff\\n{diff}\\n\\\`\\\`\\\`\`;  
          
        const prompt \= promptTemplate.replace('{diff}', diff);

        // \--- CRITICAL: Token Limit Check \---  
        // This is a rough estimate. Gemini 1.5/2.0 models have large context windows,  
        // but this check is vital. Let's assume a safe limit of 1,000,000 tokens.  
        const tokenCount \= this.estimateTokenCount(prompt);  
        if (tokenCount \> 1000000) {  
            throw new Error(\`The staged changes are too large (${tokenCount} tokens) to be processed. Please commit in smaller chunks.\`);  
        }

        const apiUrl \= \`https://generativelanguage.googleapis.com/v1beta/models/${this.model}:generateContent?key=${this.apiKey}\`;  
          
        const payload \= {  
            contents: \[{ parts: \[{ text: prompt }\] }\],  
            // Optional: Add generationConfig for temperature, etc.  
            // generationConfig: { "temperature": 0.7 }  
        };

        const response \= await fetch(apiUrl, {  
            method: 'POST',  
            headers: { 'Content-Type': 'application/json' },  
            body: JSON.stringify(payload)  
        });

        if (\!response.ok) {  
            // Provide specific error messages based on status code.  
            const errorBody \= await response.text();  
            if (response.status \=== 400) {  
                throw new Error(\`API Bad Request: The request may be malformed or the content too large. Details: ${errorBody}\`);  
            } else if (response.status \=== 429) {  
                throw new Error('API Rate Limit Exceeded. Please wait a moment and try again.');  
            }  
            throw new Error(\`API request failed with status ${response.status}: ${errorBody}\`);  
        }

        const result \= await response.json() as any;

        if (result.candidates && result.candidates.length \> 0 && result.candidates.content.parts.text) {  
            return result.candidates.content.parts.text.trim();  
        } else {  
            // Handle cases where the API returns a 200 OK but with no content or a safety block.  
            const blockReason \= result.promptFeedback?.blockReason;  
            if (blockReason) {  
                throw new Error(\`Content generation blocked due to safety settings. Reason: ${blockReason}\`);  
            }  
            throw new Error('No content found in Gemini response. The response may be empty or malformed.');  
        }  
    }  
}

// Helper to get the Git API from the built-in extension.  
async function getGitAPI() {  
    const extension \= vscode.extensions.getExtension('vscode.git');  
    if (\!extension) { return undefined; }  
    if (\!extension.isActive) { await extension.activate(); }  
    return extension.exports.getAPI(1);  
}

export function activate(context: vscode.ExtensionContext) {  
    const secretManager \= new SecretManager(context.secrets);

    // Command to set/update the API key.  
    const setApiKeyCommand \= vscode.commands.registerCommand('ai-commit-generator.setApiKey', async () \=\> {  
        const tokenInput \= await vscode.window.showInputBox({  
            prompt: 'Enter your Google Gemini API Key',  
            password: true, // Mask the input  
            ignoreFocusOut: true, // Keep open even if focus is lost  
        });  
        await secretManager.setApiKey(tokenInput);  
    });

    // Command to clear the stored API key.  
    const clearApiKeyCommand \= vscode.commands.registerCommand('ai-commit-generator.clearApiKey', async () \=\> {  
        await secretManager.clearApiKey();  
    });

    // Main command to generate the commit message.  
    const generateCommand \= vscode.commands.registerCommand('ai-commit-generator.generate', async () \=\> {  
        await vscode.window.withProgress({  
            location: vscode.ProgressLocation.Notification,  
            title: "Generating AI Commit Message...",  
            cancellable: true // Allow user to cancel  
        }, async (progress, token) \=\> {  
            try {  
                // 1\. Get API Key securely  
                let apiKey \= await secretManager.getApiKey();  
                if (\!apiKey) {  
                    const action \= await vscode.window.showWarningMessage(  
                        'Gemini API Key not found. Please set your API key to continue.',  
                        'Set API Key'  
                    );  
                    if (action \=== 'Set API Key') {  
                        await vscode.commands.executeCommand('ai-commit-generator.setApiKey');  
                    }  
                    return; // Abort if no key  
                }

                if (token.isCancellationRequested) return;

                // 2\. Get Staged Git Diff  
                const gitAPI \= await getGitAPI();  
                if (\!gitAPI |

| gitAPI.repositories.length \=== 0) {  
                    throw new Error('No Git repository found in the current workspace.');  
                }  
                  
                const repo \= gitAPI.repositories; // Simplified for single-repo workspaces.  
                const git: SimpleGit \= simpleGit(repo.rootUri.fsPath);  
                const diff \= await git.diff(\['--staged'\]);

                if (\!diff) {  
                    vscode.window.showInformationMessage('No staged changes found to generate a commit message.');  
                    return;  
                }

                if (token.isCancellationRequested) return;

                // 3\. Call Gemini API via our client  
                const apiClient \= new GeminiAPIClient(apiKey);  
                const commitMessage \= await apiClient.generateCommitMessage(diff);  
                  
                if (token.isCancellationRequested) return;

                // 4\. Populate the SCM input box  
                repo.inputBox.value \= commitMessage;  
                vscode.window.showInformationMessage('AI commit message generated\!');

            } catch (error: any) {  
                console.error(error);  
                vscode.window.showErrorMessage(\`Failed to generate commit message: ${error.message}\`);  
            }  
        });  
    });

    context.subscriptions.push(generateCommand, setApiKeyCommand, clearApiKeyCommand);  
}

export function deactivate() {}

### **4.3. Enhancing Robustness and User Experience (UX)**

Beyond the core architectural changes, several UX enhancements can elevate the extension from a functional tool to a polished product.

* **Multi-Root Workspace Support**: The current logic defaults to gitAPI.repositories. In a multi-root workspace, this is ambiguous. The code should check if gitAPI.repositories.length \> 1\. If so, it should use vscode.window.showQuickPick to present the user with a list of repository names, allowing them to select the one for which to generate the commit message.30  
* **Multiple Suggestions**: A single AI-generated message may not always be perfect. A superior workflow would be to request multiple variations from the Gemini API (by setting candidateCount in the generationConfig payload) and present them to the user in a Quick Pick list. The user could then select the most appropriate message, which would then be populated into the SCM input box.30  
* **Cancellable Progress**: The withProgress API supports cancellation. The cancellable option should be set to true. The provided cancellationToken can then be used to create an AbortSignal for the fetch request, allowing the user to cancel a long-running API call. The code should check token.isCancellationRequested at various points to exit the process early if the user cancels.  
* **Token Count Warning**: To manage user expectations, if the estimated token count of the diff is high (e.g., over 75% of the model's limit), the extension could use vscode.window.showWarningMessage to inform the user that the diff is large and the resulting commit message quality may be affected or the request may be truncated. This provides valuable transparency into the extension's operation.

#### **Works cited**

1. Extension Anatomy \- Visual Studio Code, accessed July 10, 2025, [https://code.visualstudio.com/api/get-started/extension-anatomy](https://code.visualstudio.com/api/get-started/extension-anatomy)  
2. Extension points \- vscode-docs1, accessed July 10, 2025, [https://vscode-docs1.readthedocs.io/en/latest/extensionAPI/extension-points/](https://vscode-docs1.readthedocs.io/en/latest/extensionAPI/extension-points/)  
3. Contribution Points | Visual Studio Code Extension API, accessed July 10, 2025, [https://code.visualstudio.com/api/references/contribution-points](https://code.visualstudio.com/api/references/contribution-points)  
4. Source Control in VS Code \- vscode-docs1, accessed July 10, 2025, [https://vscode-docs1.readthedocs.io/en/latest/extensionAPI/api-scm/](https://vscode-docs1.readthedocs.io/en/latest/extensionAPI/api-scm/)  
5. Source Control API \- Visual Studio Code, accessed July 10, 2025, [https://code.visualstudio.com/api/extension-guides/scm-provider](https://code.visualstudio.com/api/extension-guides/scm-provider)  
6. vscode/extensions/git/README.md at main \- GitHub, accessed July 10, 2025, [https://github.com/microsoft/vscode/blob/master/extensions/git/README.md](https://github.com/microsoft/vscode/blob/master/extensions/git/README.md)  
7. VS Code Git Extension API \- Stack Overflow, accessed July 10, 2025, [https://stackoverflow.com/questions/59442180/vs-code-git-extension-api](https://stackoverflow.com/questions/59442180/vs-code-git-extension-api)  
8. simple-git \- npm, accessed July 10, 2025, [https://www.npmjs.com/package/simple-git](https://www.npmjs.com/package/simple-git)  
9. Gemini API | Documentation | Postman API Network, accessed July 10, 2025, [https://www.postman.com/ai-on-postman/google-gemini-apis/documentation/1wnv7ft/gemini-api](https://www.postman.com/ai-on-postman/google-gemini-apis/documentation/1wnv7ft/gemini-api)  
10. Gemini 2.5 Pro | Generative AI on Vertex AI \- Google Cloud, accessed July 10, 2025, [https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-5-pro](https://cloud.google.com/vertex-ai/generative-ai/docs/models/gemini/2-5-pro)  
11. Understand and count tokens | Gemini API | Google AI for Developers, accessed July 10, 2025, [https://ai.google.dev/gemini-api/docs/tokens](https://ai.google.dev/gemini-api/docs/tokens)  
12. vscode/extensions/git/src/api/git.d.ts at main \- GitHub, accessed July 10, 2025, [https://github.com/microsoft/vscode/blob/main/extensions/git/src/api/git.d.ts](https://github.com/microsoft/vscode/blob/main/extensions/git/src/api/git.d.ts)  
13. elastic/simple-git: Project no longer maintained. \- GitHub, accessed July 10, 2025, [https://github.com/elastic/simple-git](https://github.com/elastic/simple-git)  
14. simple-git | npm \- Open Source Insights, accessed July 10, 2025, [https://deps.dev/npm/simple-git/1.96.0](https://deps.dev/npm/simple-git/1.96.0)  
15. git | Compare Similar npm Packages, accessed July 10, 2025, [https://npm-compare.com/git](https://npm-compare.com/git)  
16. simple-git vs nodegit vs git | Git Integration Libraries for Node.js Comparison, accessed July 10, 2025, [https://npm-compare.com/git,nodegit,simple-git](https://npm-compare.com/git,nodegit,simple-git)  
17. Diff \- NodeGit, accessed July 10, 2025, [https://www.nodegit.org/api/diff/](https://www.nodegit.org/api/diff/)  
18. nodegit get diff of all staged files \- javascript \- Stack Overflow, accessed July 10, 2025, [https://stackoverflow.com/questions/36245496/nodegit-get-diff-of-all-staged-files](https://stackoverflow.com/questions/36245496/nodegit-get-diff-of-all-staged-files)  
19. Running Git in JavaScript and process of picking a proper open-source project, accessed July 10, 2025, [https://dev.to/derberg/running-git-in-javascript-and-process-of-picking-a-proper-open-source-project-40fk](https://dev.to/derberg/running-git-in-javascript-and-process-of-picking-a-proper-open-source-project-40fk)  
20. statusMatrix \- isomorphic-git, accessed July 10, 2025, [https://isomorphic-git.org/docs/en/statusMatrix.html](https://isomorphic-git.org/docs/en/statusMatrix.html)  
21. Node.js Fetch, accessed July 10, 2025, [https://nodejs.org/en/learn/getting-started/fetch](https://nodejs.org/en/learn/getting-started/fetch)  
22. The Fetch API is finally stable in Node.js \- LogRocket Blog, accessed July 10, 2025, [https://blog.logrocket.com/fetch-api-node-js/](https://blog.logrocket.com/fetch-api-node-js/)  
23. Rate limits | Gemini API | Google AI for Developers, accessed July 10, 2025, [https://ai.google.dev/gemini-api/docs/rate-limits](https://ai.google.dev/gemini-api/docs/rate-limits)  
24. Rate limits and quotas | Firebase AI Logic \- Google, accessed July 10, 2025, [https://firebase.google.com/docs/ai-logic/quotas](https://firebase.google.com/docs/ai-logic/quotas)  
25. Protect keys by keeping those out of your VS Code settings \- Elio Struyf, accessed July 10, 2025, [https://www.eliostruyf.com/protect-api-auth-keys-keeping-out-vscode-settings/](https://www.eliostruyf.com/protect-api-auth-keys-keeping-out-vscode-settings/)  
26. VS Code's Token Security: Keeping Your Secrets... Not So Secretly \- Cycode, accessed July 10, 2025, [https://cycode.com/blog/exposing-vscode-secrets/](https://cycode.com/blog/exposing-vscode-secrets/)  
27. VS Code Extension Storage Explained: The What, Where, and How | by Krithika Nithyanandam | Medium, accessed July 10, 2025, [https://medium.com/@krithikanithyanandam/vs-code-extension-storage-explained-the-what-where-and-how-3a0846a632ea](https://medium.com/@krithikanithyanandam/vs-code-extension-storage-explained-the-what-where-and-how-3a0846a632ea)  
28. Why Every Developer's API Keys Are Probably in the Wrong Place And how a VS Code Extension Finally Fixed my Messy Secret Management | by Joe Wilson | Medium, accessed July 10, 2025, [https://medium.com/@dingersandks/why-every-developers-api-keys-are-probably-in-the-wrong-place-and-how-a-vs-code-extension-finally-c966d081d132](https://medium.com/@dingersandks/why-every-developers-api-keys-are-probably-in-the-wrong-place-and-how-a-vs-code-extension-finally-c966d081d132)  
29. How to use SecretStorage in your VSCode extensions \- DEV Community, accessed July 10, 2025, [https://dev.to/kompotkot/how-to-use-secretstorage-in-your-vscode-extensions-2hco](https://dev.to/kompotkot/how-to-use-secretstorage-in-your-vscode-extensions-2hco)  
30. Quick Picks | Visual Studio Code Extension API, accessed July 10, 2025, [https://code.visualstudio.com/api/ux-guidelines/quick-picks](https://code.visualstudio.com/api/ux-guidelines/quick-picks)  
31. Ordered multiple quick picks · microsoft vscode-discussions · Discussion \#567 \- GitHub, accessed July 10, 2025, [https://github.com/microsoft/vscode-discussions/discussions/567](https://github.com/microsoft/vscode-discussions/discussions/567)  
32. Gemini Developer API Pricing | Gemini API | Google AI for Developers, accessed July 10, 2025, [https://ai.google.dev/gemini-api/docs/pricing](https://ai.google.dev/gemini-api/docs/pricing)